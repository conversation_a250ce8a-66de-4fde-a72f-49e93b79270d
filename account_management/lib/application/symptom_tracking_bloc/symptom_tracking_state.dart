part of 'symptom_tracking_bloc.dart';

@freezed
class SymptomTrackingState with _$SymptomTrackingState {
  const factory SymptomTrackingState.initial() = _Initial;
  const factory SymptomTrackingState.loading() = _Loading;
  const factory SymptomTrackingState.loaded({
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) = _Loaded;
  const factory SymptomTrackingState.success() = _Success;
  const factory SymptomTrackingState.failure(PeriodTrackingFailure failure) =
      _Failure;
}
