// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symptom_tracking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SymptomTrackingEvent {
  DateTime get date => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) loadSymptomData,
    required TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)
        saveSymptomData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? loadSymptomData,
    TResult? Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? loadSymptomData,
    TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSymptomData value) loadSymptomData,
    required TResult Function(_SaveSymptomData value) saveSymptomData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSymptomData value)? loadSymptomData,
    TResult? Function(_SaveSymptomData value)? saveSymptomData,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSymptomData value)? loadSymptomData,
    TResult Function(_SaveSymptomData value)? saveSymptomData,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SymptomTrackingEventCopyWith<SymptomTrackingEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SymptomTrackingEventCopyWith<$Res> {
  factory $SymptomTrackingEventCopyWith(SymptomTrackingEvent value,
          $Res Function(SymptomTrackingEvent) then) =
      _$SymptomTrackingEventCopyWithImpl<$Res, SymptomTrackingEvent>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class _$SymptomTrackingEventCopyWithImpl<$Res,
        $Val extends SymptomTrackingEvent>
    implements $SymptomTrackingEventCopyWith<$Res> {
  _$SymptomTrackingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoadSymptomDataImplCopyWith<$Res>
    implements $SymptomTrackingEventCopyWith<$Res> {
  factory _$$LoadSymptomDataImplCopyWith(_$LoadSymptomDataImpl value,
          $Res Function(_$LoadSymptomDataImpl) then) =
      __$$LoadSymptomDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$LoadSymptomDataImplCopyWithImpl<$Res>
    extends _$SymptomTrackingEventCopyWithImpl<$Res, _$LoadSymptomDataImpl>
    implements _$$LoadSymptomDataImplCopyWith<$Res> {
  __$$LoadSymptomDataImplCopyWithImpl(
      _$LoadSymptomDataImpl _value, $Res Function(_$LoadSymptomDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$LoadSymptomDataImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$LoadSymptomDataImpl implements _LoadSymptomData {
  const _$LoadSymptomDataImpl({required this.date});

  @override
  final DateTime date;

  @override
  String toString() {
    return 'SymptomTrackingEvent.loadSymptomData(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadSymptomDataImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadSymptomDataImplCopyWith<_$LoadSymptomDataImpl> get copyWith =>
      __$$LoadSymptomDataImplCopyWithImpl<_$LoadSymptomDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) loadSymptomData,
    required TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)
        saveSymptomData,
  }) {
    return loadSymptomData(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? loadSymptomData,
    TResult? Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
  }) {
    return loadSymptomData?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? loadSymptomData,
    TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
    required TResult orElse(),
  }) {
    if (loadSymptomData != null) {
      return loadSymptomData(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSymptomData value) loadSymptomData,
    required TResult Function(_SaveSymptomData value) saveSymptomData,
  }) {
    return loadSymptomData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSymptomData value)? loadSymptomData,
    TResult? Function(_SaveSymptomData value)? saveSymptomData,
  }) {
    return loadSymptomData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSymptomData value)? loadSymptomData,
    TResult Function(_SaveSymptomData value)? saveSymptomData,
    required TResult orElse(),
  }) {
    if (loadSymptomData != null) {
      return loadSymptomData(this);
    }
    return orElse();
  }
}

abstract class _LoadSymptomData implements SymptomTrackingEvent {
  const factory _LoadSymptomData({required final DateTime date}) =
      _$LoadSymptomDataImpl;

  @override
  DateTime get date;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadSymptomDataImplCopyWith<_$LoadSymptomDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SaveSymptomDataImplCopyWith<$Res>
    implements $SymptomTrackingEventCopyWith<$Res> {
  factory _$$SaveSymptomDataImplCopyWith(_$SaveSymptomDataImpl value,
          $Res Function(_$SaveSymptomDataImpl) then) =
      __$$SaveSymptomDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime date,
      List<SymptomModel>? symptoms,
      int? painLevel,
      int? flowLevel});
}

/// @nodoc
class __$$SaveSymptomDataImplCopyWithImpl<$Res>
    extends _$SymptomTrackingEventCopyWithImpl<$Res, _$SaveSymptomDataImpl>
    implements _$$SaveSymptomDataImplCopyWith<$Res> {
  __$$SaveSymptomDataImplCopyWithImpl(
      _$SaveSymptomDataImpl _value, $Res Function(_$SaveSymptomDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? symptoms = freezed,
    Object? painLevel = freezed,
    Object? flowLevel = freezed,
  }) {
    return _then(_$SaveSymptomDataImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      symptoms: freezed == symptoms
          ? _value._symptoms
          : symptoms // ignore: cast_nullable_to_non_nullable
              as List<SymptomModel>?,
      painLevel: freezed == painLevel
          ? _value.painLevel
          : painLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      flowLevel: freezed == flowLevel
          ? _value.flowLevel
          : flowLevel // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$SaveSymptomDataImpl implements _SaveSymptomData {
  const _$SaveSymptomDataImpl(
      {required this.date,
      final List<SymptomModel>? symptoms,
      this.painLevel,
      this.flowLevel})
      : _symptoms = symptoms;

  @override
  final DateTime date;
  final List<SymptomModel>? _symptoms;
  @override
  List<SymptomModel>? get symptoms {
    final value = _symptoms;
    if (value == null) return null;
    if (_symptoms is EqualUnmodifiableListView) return _symptoms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? painLevel;
  @override
  final int? flowLevel;

  @override
  String toString() {
    return 'SymptomTrackingEvent.saveSymptomData(date: $date, symptoms: $symptoms, painLevel: $painLevel, flowLevel: $flowLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SaveSymptomDataImpl &&
            (identical(other.date, date) || other.date == date) &&
            const DeepCollectionEquality().equals(other._symptoms, _symptoms) &&
            (identical(other.painLevel, painLevel) ||
                other.painLevel == painLevel) &&
            (identical(other.flowLevel, flowLevel) ||
                other.flowLevel == flowLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date,
      const DeepCollectionEquality().hash(_symptoms), painLevel, flowLevel);

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SaveSymptomDataImplCopyWith<_$SaveSymptomDataImpl> get copyWith =>
      __$$SaveSymptomDataImplCopyWithImpl<_$SaveSymptomDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) loadSymptomData,
    required TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)
        saveSymptomData,
  }) {
    return saveSymptomData(date, symptoms, painLevel, flowLevel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? loadSymptomData,
    TResult? Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
  }) {
    return saveSymptomData?.call(date, symptoms, painLevel, flowLevel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? loadSymptomData,
    TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
    required TResult orElse(),
  }) {
    if (saveSymptomData != null) {
      return saveSymptomData(date, symptoms, painLevel, flowLevel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSymptomData value) loadSymptomData,
    required TResult Function(_SaveSymptomData value) saveSymptomData,
  }) {
    return saveSymptomData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSymptomData value)? loadSymptomData,
    TResult? Function(_SaveSymptomData value)? saveSymptomData,
  }) {
    return saveSymptomData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSymptomData value)? loadSymptomData,
    TResult Function(_SaveSymptomData value)? saveSymptomData,
    required TResult orElse(),
  }) {
    if (saveSymptomData != null) {
      return saveSymptomData(this);
    }
    return orElse();
  }
}

abstract class _SaveSymptomData implements SymptomTrackingEvent {
  const factory _SaveSymptomData(
      {required final DateTime date,
      final List<SymptomModel>? symptoms,
      final int? painLevel,
      final int? flowLevel}) = _$SaveSymptomDataImpl;

  @override
  DateTime get date;
  List<SymptomModel>? get symptoms;
  int? get painLevel;
  int? get flowLevel;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SaveSymptomDataImplCopyWith<_$SaveSymptomDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SymptomTrackingState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SymptomTrackingStateCopyWith<$Res> {
  factory $SymptomTrackingStateCopyWith(SymptomTrackingState value,
          $Res Function(SymptomTrackingState) then) =
      _$SymptomTrackingStateCopyWithImpl<$Res, SymptomTrackingState>;
}

/// @nodoc
class _$SymptomTrackingStateCopyWithImpl<$Res,
        $Val extends SymptomTrackingState>
    implements $SymptomTrackingStateCopyWith<$Res> {
  _$SymptomTrackingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$SymptomTrackingStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'SymptomTrackingState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements SymptomTrackingState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$SymptomTrackingStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'SymptomTrackingState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements SymptomTrackingState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<SymptomModel>? symptoms, int? painLevel, int? flowLevel});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$SymptomTrackingStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symptoms = freezed,
    Object? painLevel = freezed,
    Object? flowLevel = freezed,
  }) {
    return _then(_$LoadedImpl(
      symptoms: freezed == symptoms
          ? _value._symptoms
          : symptoms // ignore: cast_nullable_to_non_nullable
              as List<SymptomModel>?,
      painLevel: freezed == painLevel
          ? _value.painLevel
          : painLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      flowLevel: freezed == flowLevel
          ? _value.flowLevel
          : flowLevel // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl(
      {final List<SymptomModel>? symptoms, this.painLevel, this.flowLevel})
      : _symptoms = symptoms;

  final List<SymptomModel>? _symptoms;
  @override
  List<SymptomModel>? get symptoms {
    final value = _symptoms;
    if (value == null) return null;
    if (_symptoms is EqualUnmodifiableListView) return _symptoms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? painLevel;
  @override
  final int? flowLevel;

  @override
  String toString() {
    return 'SymptomTrackingState.loaded(symptoms: $symptoms, painLevel: $painLevel, flowLevel: $flowLevel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(other._symptoms, _symptoms) &&
            (identical(other.painLevel, painLevel) ||
                other.painLevel == painLevel) &&
            (identical(other.flowLevel, flowLevel) ||
                other.flowLevel == flowLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_symptoms), painLevel, flowLevel);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return loaded(symptoms, painLevel, flowLevel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return loaded?.call(symptoms, painLevel, flowLevel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(symptoms, painLevel, flowLevel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements SymptomTrackingState {
  const factory _Loaded(
      {final List<SymptomModel>? symptoms,
      final int? painLevel,
      final int? flowLevel}) = _$LoadedImpl;

  List<SymptomModel>? get symptoms;
  int? get painLevel;
  int? get flowLevel;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<$Res> {
  factory _$$SuccessImplCopyWith(
          _$SuccessImpl value, $Res Function(_$SuccessImpl) then) =
      __$$SuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<$Res>
    extends _$SymptomTrackingStateCopyWithImpl<$Res, _$SuccessImpl>
    implements _$$SuccessImplCopyWith<$Res> {
  __$$SuccessImplCopyWithImpl(
      _$SuccessImpl _value, $Res Function(_$SuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SuccessImpl implements _Success {
  const _$SuccessImpl();

  @override
  String toString() {
    return 'SymptomTrackingState.success()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return success();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return success?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _Success implements SymptomTrackingState {
  const factory _Success() = _$SuccessImpl;
}

/// @nodoc
abstract class _$$FailureImplCopyWith<$Res> {
  factory _$$FailureImplCopyWith(
          _$FailureImpl value, $Res Function(_$FailureImpl) then) =
      __$$FailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureImplCopyWithImpl<$Res>
    extends _$SymptomTrackingStateCopyWithImpl<$Res, _$FailureImpl>
    implements _$$FailureImplCopyWith<$Res> {
  __$$FailureImplCopyWithImpl(
      _$FailureImpl _value, $Res Function(_$FailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_$FailureImpl(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureImpl implements _Failure {
  const _$FailureImpl(this.failure);

  @override
  final PeriodTrackingFailure failure;

  @override
  String toString() {
    return 'SymptomTrackingState.failure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureImpl &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      __$$FailureImplCopyWithImpl<_$FailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
  }) {
    return failure(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
  }) {
    return failure?.call(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)?
        loaded,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _Failure implements SymptomTrackingState {
  const factory _Failure(final PeriodTrackingFailure failure) = _$FailureImpl;

  PeriodTrackingFailure get failure;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureImplCopyWith<_$FailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
