import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_route/auto_route.dart';
import 'package:intl/intl.dart';

import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:design_system/design/theme.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';

import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/weekly_calendar.dart';
import '../../custom_widgets/menstural_cycle_dial.dart';
import '../../services/cycle_day_calculator.dart';
import 'daily_symptom_page.dart';

@RoutePage()
class DailySymptomTrackingPage extends StatefulWidget {
  final DateTime? initialDate;

  const DailySymptomTrackingPage({Key? key, this.initialDate})
      : super(key: key);

  @override
  State<DailySymptomTrackingPage> createState() =>
      _DailySymptomTrackingPageState();
}

class _DailySymptomTrackingPageState extends State<DailySymptomTrackingPage> {
  late DateTime _selectedDate;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate ?? DateTime.now();

    // Watch period tracking data for the current year
    context.read<PeriodTrackingWatcherBloc>().add(
          PeriodTrackingWatcherEvent.watchYearStarted(_selectedDate.year),
        );
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Color(0xffFBF0D5),
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: Color(0xffFAF2DF),
        subtitle: 'Daily Symptom Tracking',
        height: .35.sw,
        topLeftIcon: GestureDetector(
          onTap: () {
            context.router.pop();
          },
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Icon(
                Icons.arrow_back,
                color: Color(0xff30285D),
              ),
            ),
          ),
        ),
        topRightIcon: GestureDetector(
          onTap: () {
            context.router.push(PeriodTrackingViewRoute());
          },
          child: Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              color: Color(0xffFAF2DF),
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Icon(
                Icons.calendar_today_rounded,
                color: Color(0xff30285D),
              ),
            ),
          ),
        ),
      ),
      body: BlocListener<SymptomTrackingBloc, SymptomTrackingState>(
        listener: (context, state) {
          state.when(
            initial: (selectedDate) {
              if (selectedDate != null && selectedDate != _selectedDate) {
                setState(() {
                  _selectedDate = selectedDate;
                });
              }
            },
            loading: (selectedDate) {
              if (selectedDate != null && selectedDate != _selectedDate) {
                setState(() {
                  _selectedDate = selectedDate;
                });
              }
            },
            loaded: (selectedDate, _, __, ___) {
              if (selectedDate != _selectedDate) {
                setState(() {
                  _selectedDate = selectedDate;
                });
              }
            },
            success: (selectedDate) {
              if (selectedDate != _selectedDate) {
                setState(() {
                  _selectedDate = selectedDate;
                });
              }
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Symptoms saved successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            failure: (selectedDate, failure) {
              if (selectedDate != null && selectedDate != _selectedDate) {
                setState(() {
                  _selectedDate = selectedDate;
                });
              }
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save symptoms'),
                  backgroundColor: Colors.red,
                ),
              );
            },
          );
        },
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            children: [
              SizedBox(height: .38.sw),

              // Weekly Calendar
              BlocBuilder<PeriodTrackingWatcherBloc,
                  PeriodTrackingWatcherState>(
                builder: (context, state) {
                  return state.when(
                    initial: () => Container(),
                    loading: () => Container(
                        height: 120.h,
                        child: Center(child: CircularProgressIndicator())),
                    loadSuccess: (yearData) {
                      // Extract period and ovulation dates
                      final periodDates = <DateTime>{};
                      final ovulationDates = <DateTime>{};

                      for (final monthData in yearData.values) {
                        for (final dayData in monthData.values) {
                          if (dayData.isPeriodDate == true &&
                              dayData.date != null) {
                            periodDates.add(dayData.date!);
                          }
                          if (dayData.isOvulationDate == true &&
                              dayData.date != null) {
                            ovulationDates.add(dayData.date!);
                          }
                        }
                      }

                      return WeeklyCalendar(
                        selectedDate: _selectedDate,
                        onDateSelected: _onDateSelected,
                        periodDates: periodDates,
                        ovulationDates: ovulationDates,
                      );
                    },
                    loadFailure: (failure) => Container(
                      height: 120.h,
                      child:
                          Center(child: Text('Failed to load calendar data')),
                    ),
                  );
                },
              ),

              SizedBox(height: 24.h),

              // Selected Date Info
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Text(
                      DateFormat('EEEE, MMMM d, yyyy').format(_selectedDate),
                      style: GoogleFonts.mulish(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: Color(0xff2D2D2D),
                      ),
                    ),
                    SizedBox(height: 16.h),

                    // Menstrual Cycle Dial
                    BlocBuilder<PeriodTrackingWatcherBloc,
                        PeriodTrackingWatcherState>(
                      builder: (context, state) {
                        return state.when(
                          initial: () => Container(),
                          loading: () => Container(
                            height: 200.h,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                          loadSuccess: (yearData) {
                            final cycleInfo =
                                CycleDayCalculator.calculateCycleDay(
                                    _selectedDate, yearData);

                            if (cycleInfo == null) {
                              return Container(
                                height: 200.h,
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.calendar_today,
                                          size: 48, color: Colors.grey),
                                      SizedBox(height: 8.h),
                                      Text(
                                        'No cycle data available',
                                        style: GoogleFonts.mulish(
                                          fontSize: 16.sp,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }

                            return Column(
                              children: [
                                // Enhanced menstrual cycle tracker using calculated data
                                MenstrualCycleTracker.fromCycleInfo(
                                  cycleInfo: cycleInfo,
                                  size: 200,
                                  textStyle: GoogleFonts.mulish(
                                    color: Colors.white,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  outerCircleColor: Color(0xffD3CFC6),
                                  periodArcColor: AppTheme.primaryColor,
                                  ovulationArcColor: Color(0xffECA83D),
                                  innerCircleColor: AppTheme.primaryColor,
                                  pointerColor: Colors.white,
                                ),
                                SizedBox(height: 16.h),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16.w, vertical: 8.h),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    '${cycleInfo.cyclePhase} Phase',
                                    style: GoogleFonts.mulish(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                          loadFailure: (failure) => Container(
                            height: 200.h,
                            child: Center(
                                child: Text('Failed to load cycle data')),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // Add Symptoms Button
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          showModalBottomSheet<void>(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => BlocProvider(
                              create: (context) {
                                final bloc = getIt<SymptomTrackingBloc>();
                                // Initialize the bloc with the current selected date
                                bloc.add(SymptomTrackingEvent.loadSymptomData(
                                    date: _selectedDate));
                                return bloc;
                              },
                              child: Container(
                                height: .9.sh,
                                child: DailySymptomPage(),
                              ),
                            ),
                          );
                        },
                        icon: Icon(Icons.add, color: Colors.white),
                        label: Text(
                          'Add Symptoms',
                          style: GoogleFonts.mulish(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),
            ],
          ),
        ),
      ),
    );
  }
}
