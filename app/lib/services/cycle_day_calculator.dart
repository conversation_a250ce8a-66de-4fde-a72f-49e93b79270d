import 'package:account_management/domain/model/period_tracking_model.dart';

class CycleDayCalculator {
  /// Calculates which day of the menstrual cycle a given date represents
  /// Returns null if the date is not part of any identifiable cycle
  static CycleDayInfo? calculateCycleDay(
    DateTime targetDate,
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) {
    // Get all period dates from the year data
    final periodDates = <DateTime>[];

    for (final monthData in yearData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isPeriodDate == true && dayData.date != null) {
          periodDates.add(dayData.date!);
        }
      }
    }

    if (periodDates.isEmpty) {
      return null;
    }

    // Sort period dates
    periodDates.sort();

    // Find the cycle that contains the target date
    DateTime? cycleStart;
    DateTime? nextCycleStart;

    for (int i = 0; i < periodDates.length; i++) {
      final periodDate = periodDates[i];

      // Check if this is the start of a new cycle (first period date or gap > 10 days from previous)
      bool isNewCycle = i == 0;
      if (i > 0) {
        final daysSincePrevious =
            periodDate.difference(periodDates[i - 1]).inDays;
        if (daysSincePrevious > 10) {
          isNewCycle = true;
        }
      }

      if (isNewCycle) {
        // This is a cycle start
        if (targetDate.isAfter(periodDate.subtract(Duration(days: 1))) ||
            targetDate.isAtSameMomentAs(periodDate)) {
          cycleStart = periodDate;

          // Find the next cycle start
          for (int j = i + 1; j < periodDates.length; j++) {
            final nextPeriodDate = periodDates[j];
            final daysSinceCurrent =
                nextPeriodDate.difference(periodDate).inDays;
            if (daysSinceCurrent > 10) {
              nextCycleStart = nextPeriodDate;
              break;
            }
          }

          // If target date is before the next cycle start (or no next cycle), this is our cycle
          if (nextCycleStart == null || targetDate.isBefore(nextCycleStart)) {
            break;
          }
        }
      }
    }

    if (cycleStart == null) {
      return null;
    }

    // Calculate the cycle day
    final daysDifference = targetDate.difference(cycleStart).inDays + 1;

    // Determine cycle length (default to 28 if no next cycle)
    int cycleLength = 28;
    if (nextCycleStart != null) {
      final calculatedLength = nextCycleStart.difference(cycleStart).inDays;
      // Ensure cycle length is reasonable (between 21 and 45 days)
      if (calculatedLength >= 21 && calculatedLength <= 45) {
        cycleLength = calculatedLength;
      }
    }

    // Ensure cycle day is valid (between 1 and cycle length)
    final validCycleDay = daysDifference.clamp(1, cycleLength);

    // Determine if it's a period day
    bool isPeriodDay = false;
    for (final periodDate in periodDates) {
      if (_isSameDay(targetDate, periodDate)) {
        isPeriodDay = true;
        break;
      }
    }

    // Determine if it's an ovulation day (only check actual database data)
    bool isOvulationDay = false;
    for (final monthData in yearData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isOvulationDate == true &&
            dayData.date != null &&
            _isSameDay(targetDate, dayData.date!)) {
          isOvulationDay = true;
          break;
        }
      }
      if (isOvulationDay) break;
    }

    return CycleDayInfo(
      cycleDay: validCycleDay,
      cycleLength: cycleLength,
      isPeriodDay: isPeriodDay,
      isOvulationDay: isOvulationDay,
      cycleStartDate: cycleStart,
    );
  }

  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}

class CycleDayInfo {
  final int cycleDay;
  final int cycleLength;
  final bool isPeriodDay;
  final bool isOvulationDay;
  final DateTime cycleStartDate;

  const CycleDayInfo({
    required this.cycleDay,
    required this.cycleLength,
    required this.isPeriodDay,
    required this.isOvulationDay,
    required this.cycleStartDate,
  });

  String get cyclePhase {
    if (isPeriodDay) return 'Menstrual';
    if (cycleDay <= 5) return 'Menstrual';
    if (isOvulationDay) return 'Ovulation';
    if (cycleDay <= 13) return 'Follicular';
    return 'Luteal';
  }
}
